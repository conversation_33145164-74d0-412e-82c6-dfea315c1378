@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: 196 28 31; /* #C41C1F in RGB for better Tailwind integration */
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: 196 28 31; /* #C41C1F in RGB */
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: 196 28 31; /* #C41C1F in RGB */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: 196 28 31; /* #C41C1F in RGB */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* Applex brand colors - Enhanced system */
  --applex-primary: 196 28 31; /* #C41C1F */
  --applex-primary-light: 229 62 62; /* #E53E3E */
  --applex-primary-dark: 155 28 28; /* #9B1C1C */
  --applex-secondary: 26 32 44; /* #1A202C */
  --applex-gray-50: 247 250 252; /* #F7FAFC */
  --applex-gray-100: 237 242 247; /* #EDF2F7 */
  --applex-gray-200: 226 232 240; /* #E2E8F0 */
  --applex-gray-300: 203 213 225; /* #CBD5E1 */
  --applex-gray-400: 148 163 184; /* #94A3B8 */
  --applex-gray-500: 100 116 139; /* #64748B */
  --applex-gray-600: 71 85 105; /* #475569 */
  --applex-gray-700: 51 65 85; /* #334155 */
  --applex-gray-800: 30 41 59; /* #1E293B */
  --applex-gray-900: 15 23 42; /* #0F172A */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: 196 28 31; /* #C41C1F in RGB */
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: 196 28 31; /* #C41C1F in RGB */
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: 196 28 31; /* #C41C1F in RGB */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: 196 28 31; /* #C41C1F in RGB */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Glassmorphism utilities */
  .glass {
    @apply backdrop-blur-md bg-white/80 border border-white/20;
  }

  .glass-dark {
    @apply backdrop-blur-md bg-gray-900/80 border border-gray-700/20;
  }

  /* Modern gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, rgb(var(--applex-primary)) 0%, rgb(var(--applex-primary-dark)) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, rgb(var(--applex-gray-50)) 0%, rgb(var(--applex-gray-100)) 100%);
  }

  /* Enhanced shadows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(var(--applex-primary), 0.15);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(var(--applex-primary), 0.2);
  }

  /* Modern card styles */
  .card-modern {
    @apply bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  /* Text gradients */
  .text-gradient {
    background: linear-gradient(135deg, rgb(var(--applex-primary)) 0%, rgb(var(--applex-primary-dark)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Animated elements */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(var(--applex-primary)) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(var(--applex-primary));
    border-radius: 3px;
  }
}

@layer utilities {
  /* Custom animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  /* Responsive text sizes */
  .text-responsive-xl {
    @apply text-2xl md:text-3xl lg:text-4xl xl:text-5xl;
  }

  .text-responsive-lg {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  .text-responsive-base {
    @apply text-base md:text-lg lg:text-xl;
  }
}
