import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Code, Zap, Shield } from "lucide-react";

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-gray-50 to-red-50 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C41C1F' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center max-w-5xl mx-auto">
          {/* Logo/Brand */}
          <div className="mb-8">
            <h1 className="text-6xl md:text-8xl font-bold text-[#C41C1F] mb-4 tracking-tight">
              Applex
            </h1>
            <div className="w-24 h-1 bg-[#C41C1F] mx-auto rounded-full"></div>
          </div>

          {/* Main Headline */}
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Innovative Software
            <span className="block text-[#C41C1F]">Solutions</span>
          </h2>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            Transforming ideas into powerful digital experiences. We craft cutting-edge software solutions that drive business growth and innovation.
          </p>

          {/* Feature Icons */}
          <div className="flex justify-center space-x-8 mb-12">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-[#C41C1F] rounded-full flex items-center justify-center mb-2">
                <Code className="w-8 h-8 text-white" />
              </div>
              <span className="text-sm font-medium text-gray-700">Custom Development</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-[#C41C1F] rounded-full flex items-center justify-center mb-2">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <span className="text-sm font-medium text-gray-700">Fast Delivery</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-[#C41C1F] rounded-full flex items-center justify-center mb-2">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <span className="text-sm font-medium text-gray-700">Secure Solutions</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              size="lg" 
              className="bg-[#C41C1F] hover:bg-[#9B1C1C] text-white px-8 py-4 text-lg font-semibold rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Start Your Project
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-[#C41C1F] text-[#C41C1F] hover:bg-[#C41C1F] hover:text-white px-8 py-4 text-lg font-semibold rounded-full transition-all duration-300"
            >
              View Our Work
            </Button>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-[#C41C1F] mb-1">500+</div>
              <div className="text-sm text-gray-600">Projects Delivered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-[#C41C1F] mb-1">50+</div>
              <div className="text-sm text-gray-600">Happy Clients</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-[#C41C1F] mb-1">5+</div>
              <div className="text-sm text-gray-600">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-[#C41C1F] mb-1">24/7</div>
              <div className="text-sm text-gray-600">Support</div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-[#C41C1F] opacity-10 rounded-full animate-bounce"></div>
      <div className="absolute bottom-20 right-10 w-16 h-16 bg-[#C41C1F] opacity-10 rounded-full animate-pulse"></div>
      <div className="absolute top-1/2 left-5 w-12 h-12 bg-[#C41C1F] opacity-10 rounded-full animate-ping"></div>
    </section>
  );
}
