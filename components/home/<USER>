import { Card } from "@/components/ui/card";
import { Users, Target, Award, Lightbulb } from "lucide-react";

export default function About() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              About <span className="text-[#C41C1F]">Applex</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We are a passionate team of software developers, designers, and innovators dedicated to creating exceptional digital solutions that transform businesses and enhance user experiences.
            </p>
          </div>

          {/* Main Content Grid */}
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            {/* Left Content */}
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                Crafting Digital Excellence Since 2019
              </h3>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                At Applex, we believe that great software is more than just code – it's about understanding your business needs, your users' expectations, and the technology landscape that drives innovation.
              </p>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                Our team combines technical expertise with creative problem-solving to deliver solutions that not only meet your requirements but exceed your expectations. From web applications to mobile apps, from enterprise software to cloud solutions, we've got you covered.
              </p>
              
              {/* Key Points */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-[#C41C1F] rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">Agile Development Methodology</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-[#C41C1F] rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">Latest Technology Stack</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-[#C41C1F] rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">Scalable & Maintainable Code</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-[#C41C1F] rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">24/7 Support & Maintenance</span>
                </div>
              </div>
            </div>

            {/* Right Content - Image Placeholder */}
            <div className="relative">
              <div className="bg-gradient-to-br from-[#C41C1F] to-[#9B1C1C] rounded-2xl p-8 text-white">
                <div className="text-center">
                  <div className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Lightbulb className="w-12 h-12 text-white" />
                  </div>
                  <h4 className="text-2xl font-bold mb-4">Our Mission</h4>
                  <p className="text-lg opacity-90 leading-relaxed">
                    To empower businesses with innovative software solutions that drive growth, efficiency, and success in the digital age.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Values Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="p-6 text-center hover:shadow-lg transition-shadow duration-300 border-2 hover:border-[#C41C1F]">
              <div className="w-16 h-16 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-[#C41C1F]" />
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-3">Team Collaboration</h4>
              <p className="text-gray-600">
                We work closely with our clients as an extension of their team, ensuring seamless communication and collaboration.
              </p>
            </Card>

            <Card className="p-6 text-center hover:shadow-lg transition-shadow duration-300 border-2 hover:border-[#C41C1F]">
              <div className="w-16 h-16 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="w-8 h-8 text-[#C41C1F]" />
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-3">Goal-Oriented</h4>
              <p className="text-gray-600">
                Every project is approached with clear objectives and measurable outcomes to ensure maximum value delivery.
              </p>
            </Card>

            <Card className="p-6 text-center hover:shadow-lg transition-shadow duration-300 border-2 hover:border-[#C41C1F]">
              <div className="w-16 h-16 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-[#C41C1F]" />
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-3">Quality First</h4>
              <p className="text-gray-600">
                We maintain the highest standards of code quality, testing, and documentation in every project we deliver.
              </p>
            </Card>

            <Card className="p-6 text-center hover:shadow-lg transition-shadow duration-300 border-2 hover:border-[#C41C1F]">
              <div className="w-16 h-16 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Lightbulb className="w-8 h-8 text-[#C41C1F]" />
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-3">Innovation</h4>
              <p className="text-gray-600">
                We stay ahead of technology trends and continuously explore new ways to solve complex challenges.
              </p>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
