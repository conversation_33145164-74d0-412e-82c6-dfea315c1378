import { Card } from "@/components/ui/card";
import { 
  Zap, 
  Shield, 
  Smartphone, 
  Globe, 
  Users, 
  BarChart3,
  Clock,
  HeadphonesIcon
} from "lucide-react";

export default function Features() {
  const features = [
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Optimized performance with cutting-edge technologies for blazing fast load times and smooth user experiences."
    },
    {
      icon: Shield,
      title: "Secure & Reliable",
      description: "Enterprise-grade security measures and robust architecture ensure your data and applications are always protected."
    },
    {
      icon: Smartphone,
      title: "Mobile First",
      description: "Responsive designs that work perfectly across all devices, from smartphones to desktop computers."
    },
    {
      icon: Globe,
      title: "Global Reach",
      description: "Scalable solutions built to handle global traffic with CDN integration and multi-region deployment."
    },
    {
      icon: Users,
      title: "User-Centric",
      description: "Intuitive interfaces designed with user experience in mind, ensuring high engagement and satisfaction."
    },
    {
      icon: BarChart3,
      title: "Analytics Ready",
      description: "Built-in analytics and reporting capabilities to track performance and make data-driven decisions."
    },
    {
      icon: Clock,
      title: "Real-time Updates",
      description: "Live data synchronization and real-time features to keep your users engaged and informed."
    },
    {
      icon: HeadphonesIcon,
      title: "24/7 Support",
      description: "Round-the-clock technical support and maintenance to ensure your applications run smoothly."
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Why Choose <span className="text-[#C41C1F]">Applex</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We deliver exceptional software solutions with features that set us apart from the competition. Here's what makes us the right choice for your next project.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <Card key={index} className="p-6 text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border-2 hover:border-[#C41C1F] group">
                  <div className="w-16 h-16 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-[#C41C1F] transition-colors duration-300">
                    <IconComponent className="w-8 h-8 text-[#C41C1F] group-hover:text-white transition-colors duration-300" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-[#C41C1F] transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </Card>
              );
            })}
          </div>

          {/* Technology Stack */}
          <div className="bg-gradient-to-r from-[#C41C1F] to-[#9B1C1C] rounded-2xl p-8 md:p-12 text-white">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold mb-4">
                Our Technology Stack
              </h3>
              <p className="text-lg opacity-90 max-w-2xl mx-auto">
                We use the latest and most reliable technologies to build robust, scalable, and maintainable software solutions.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* Frontend */}
              <div className="text-center">
                <h4 className="text-xl font-bold mb-4">Frontend</h4>
                <div className="space-y-2">
                  {["React.js", "Next.js", "TypeScript", "Tailwind CSS", "Vue.js", "Angular"].map((tech, index) => (
                    <div key={index} className="bg-white bg-opacity-20 rounded-lg py-2 px-4 text-sm">
                      {tech}
                    </div>
                  ))}
                </div>
              </div>

              {/* Backend */}
              <div className="text-center">
                <h4 className="text-xl font-bold mb-4">Backend</h4>
                <div className="space-y-2">
                  {["Node.js", "Python", "Java", "C#", "PHP", "Go"].map((tech, index) => (
                    <div key={index} className="bg-white bg-opacity-20 rounded-lg py-2 px-4 text-sm">
                      {tech}
                    </div>
                  ))}
                </div>
              </div>

              {/* Database & Cloud */}
              <div className="text-center">
                <h4 className="text-xl font-bold mb-4">Database & Cloud</h4>
                <div className="space-y-2">
                  {["PostgreSQL", "MongoDB", "Redis", "AWS", "Azure", "Docker"].map((tech, index) => (
                    <div key={index} className="bg-white bg-opacity-20 rounded-lg py-2 px-4 text-sm">
                      {tech}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-[#C41C1F] mb-2">99.9%</div>
              <div className="text-gray-600">Uptime Guarantee</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-[#C41C1F] mb-2">&lt;2s</div>
              <div className="text-gray-600">Average Load Time</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-[#C41C1F] mb-2">100%</div>
              <div className="text-gray-600">Client Satisfaction</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-[#C41C1F] mb-2">24/7</div>
              <div className="text-gray-600">Technical Support</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
