"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Target, Award, Lightbulb, CheckCircle } from "lucide-react";
import { useState } from "react";

export default function About() {
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-primary/5 to-transparent"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-20">
            <Badge variant="outline" className="mb-4 border-primary/20 text-primary bg-primary/5">
              About Applex
            </Badge>
            <h2 className="text-responsive-xl font-bold text-gray-900 mb-6">
              Transforming Ideas Into
              <span className="block text-gradient">Digital Reality</span>
            </h2>
            <p className="text-responsive-base text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We are a passionate team of software developers, designers, and innovators dedicated to creating exceptional digital solutions that transform businesses and enhance user experiences.
            </p>
          </div>

          {/* Main Content Grid */}
          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            {/* Left Content */}
            <div className="space-y-8">
              <div>
                <h3 className="text-3xl font-bold text-gray-900 mb-6">
                  Crafting Digital Excellence Since 2019
                </h3>
                <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                  At Applex, we believe that great software is more than just code – it's about understanding your business needs, your users' expectations, and the technology landscape that drives innovation.
                </p>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Our team combines technical expertise with creative problem-solving to deliver solutions that not only meet your requirements but exceed your expectations.
                </p>
              </div>

              {/* Key Points */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  "Agile Development Methodology",
                  "Latest Technology Stack",
                  "Scalable & Maintainable Code",
                  "24/7 Support & Maintenance"
                ].map((point, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-700 font-medium">{point}</span>
                  </div>
                ))}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-gray-100">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">500+</div>
                  <div className="text-sm text-gray-600">Projects Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">50+</div>
                  <div className="text-sm text-gray-600">Happy Clients</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">5+</div>
                  <div className="text-sm text-gray-600">Years Experience</div>
                </div>
              </div>
            </div>

            {/* Right Content - Enhanced Image */}
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  alt="Team collaboration"
                  className={`w-full h-[400px] object-cover transition-all duration-700 ${imageLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-105'}`}
                  onLoad={() => setImageLoaded(true)}
                />
                <div className="absolute inset-0 gradient-primary opacity-90"></div>
                <div className="absolute inset-0 flex items-center justify-center text-white text-center p-8">
                  <div>
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
                      <Lightbulb className="w-10 h-10 text-white" />
                    </div>
                    <h4 className="text-2xl font-bold mb-4">Our Mission</h4>
                    <p className="text-lg opacity-90 leading-relaxed">
                      To empower businesses with innovative software solutions that drive growth, efficiency, and success in the digital age.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Values Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                icon: Users,
                title: "Team Collaboration",
                description: "We work closely with our clients as an extension of their team, ensuring seamless communication and collaboration."
              },
              {
                icon: Target,
                title: "Goal-Oriented",
                description: "Every project is approached with clear objectives and measurable outcomes to ensure maximum value delivery."
              },
              {
                icon: Award,
                title: "Quality First",
                description: "We maintain the highest standards of code quality, testing, and documentation in every project we deliver."
              },
              {
                icon: Lightbulb,
                title: "Innovation",
                description: "We stay ahead of technology trends and continuously explore new ways to solve complex challenges."
              }
            ].map((value, index) => {
              const IconComponent = value.icon;
              return (
                <Card key={index} className="card-modern p-6 text-center hover:shadow-glow transition-all duration-300 hover:-translate-y-2 border-2 hover:border-primary group">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary transition-colors duration-300">
                    <IconComponent className="w-8 h-8 text-primary group-hover:text-white transition-colors duration-300" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary transition-colors duration-300">
                    {value.title}
                  </h4>
                  <p className="text-gray-600 leading-relaxed">
                    {value.description}
                  </p>
                </Card>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
