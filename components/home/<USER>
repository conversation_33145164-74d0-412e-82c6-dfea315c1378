"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Globe,
  Smartphone,
  Cloud,
  Database,
  ShoppingCart,
  Cog,
  ArrowRight,
  CheckCircle,
  Sparkles
} from "lucide-react";
import { useState } from "react";

export default function Services() {
  const [hoveredService, setHoveredService] = useState<number | null>(null);

  const services = [
    {
      icon: Globe,
      title: "Web Development",
      description: "Custom web applications built with modern frameworks and technologies for optimal performance and user experience.",
      features: ["React/Next.js", "Node.js Backend", "Responsive Design", "SEO Optimized"],
      gradient: "from-blue-500 to-blue-600",
      popular: false
    },
    {
      icon: Smartphone,
      title: "Mobile App Development",
      description: "Native and cross-platform mobile applications that deliver exceptional user experiences across all devices.",
      features: ["iOS & Android", "React Native", "Flutter", "App Store Deployment"],
      gradient: "from-green-500 to-green-600",
      popular: true
    },
    {
      icon: Cloud,
      title: "Cloud Solutions",
      description: "Scalable cloud infrastructure and migration services to ensure your applications perform at their best.",
      features: ["AWS/Azure/GCP", "DevOps", "Microservices", "Auto-scaling"],
      gradient: "from-purple-500 to-purple-600",
      popular: false
    },
    {
      icon: Database,
      title: "Database Design",
      description: "Robust database architecture and optimization for efficient data management and lightning-fast queries.",
      features: ["SQL/NoSQL", "Data Modeling", "Performance Tuning", "Backup Solutions"],
      gradient: "from-orange-500 to-orange-600",
      popular: false
    },
    {
      icon: ShoppingCart,
      title: "E-commerce Solutions",
      description: "Complete e-commerce platforms with payment integration, inventory management, and analytics.",
      features: ["Payment Gateway", "Inventory System", "Analytics", "Mobile Commerce"],
      gradient: "from-pink-500 to-pink-600",
      popular: false
    },
    {
      icon: Cog,
      title: "Custom Software",
      description: "Tailored software solutions designed specifically for your business processes and requirements.",
      features: ["Business Logic", "API Integration", "Workflow Automation", "Custom Features"],
      gradient: "from-indigo-500 to-indigo-600",
      popular: false
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our <span className="text-[#C41C1F]">Services</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We offer comprehensive software development services to help your business thrive in the digital landscape. From concept to deployment, we've got you covered.
            </p>
          </div>

          {/* Services Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <Card key={index} className="p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-2 hover:border-[#C41C1F] group">
                  {/* Icon */}
                  <div className="mb-6">
                    <div className="w-16 h-16 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center group-hover:bg-[#C41C1F] transition-colors duration-300">
                      <IconComponent className="w-8 h-8 text-[#C41C1F] group-hover:text-white transition-colors duration-300" />
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#C41C1F] transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features */}
                  <div className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-[#C41C1F] mr-2 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  <Button 
                    variant="outline" 
                    className="w-full border-[#C41C1F] text-[#C41C1F] hover:bg-[#C41C1F] hover:text-white transition-all duration-300"
                  >
                    Learn More
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                </Card>
              );
            })}
          </div>

          {/* Process Section */}
          <div className="bg-white rounded-2xl p-8 md:p-12 shadow-lg">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Our Development <span className="text-[#C41C1F]">Process</span>
              </h3>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                We follow a proven methodology to ensure your project is delivered on time, within budget, and exceeds expectations.
              </p>
            </div>

            <div className="grid md:grid-cols-4 gap-8">
              {[
                { step: "01", title: "Discovery", description: "Understanding your requirements and goals" },
                { step: "02", title: "Planning", description: "Creating detailed project roadmap and timeline" },
                { step: "03", title: "Development", description: "Building your solution with regular updates" },
                { step: "04", title: "Deployment", description: "Launching and providing ongoing support" }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-[#C41C1F] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    {phase.step}
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 mb-2">{phase.title}</h4>
                  <p className="text-gray-600 text-sm">{phase.description}</p>
                  {index < 3 && (
                    <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-[#C41C1F] opacity-20 transform translate-x-4"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
