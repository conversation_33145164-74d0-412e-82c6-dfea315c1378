import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Send,
  MessageSquare,
  Calendar,
  ArrowRight
} from "lucide-react";

export default function Contact() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Let's Start Your <span className="text-[#C41C1F]">Project</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Ready to transform your ideas into reality? Get in touch with our team and let's discuss how we can help you achieve your goals.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <Card className="p-8 shadow-lg border-2 hover:border-[#C41C1F] transition-colors duration-300">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h3>
                
                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#C41C1F] focus:border-[#C41C1F] transition-colors duration-300"
                        placeholder="John"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#C41C1F] focus:border-[#C41C1F] transition-colors duration-300"
                        placeholder="Doe"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#C41C1F] focus:border-[#C41C1F] transition-colors duration-300"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#C41C1F] focus:border-[#C41C1F] transition-colors duration-300"
                      placeholder="+****************"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Project Type
                    </label>
                    <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#C41C1F] focus:border-[#C41C1F] transition-colors duration-300">
                      <option value="">Select a service</option>
                      <option value="web">Web Development</option>
                      <option value="mobile">Mobile App Development</option>
                      <option value="cloud">Cloud Solutions</option>
                      <option value="ecommerce">E-commerce</option>
                      <option value="custom">Custom Software</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Project Details *
                    </label>
                    <textarea
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#C41C1F] focus:border-[#C41C1F] transition-colors duration-300"
                      placeholder="Tell us about your project requirements, timeline, and budget..."
                      required
                    ></textarea>
                  </div>

                  <Button 
                    type="submit"
                    className="w-full bg-[#C41C1F] hover:bg-[#9B1C1C] text-white py-3 text-lg font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    Send Message
                    <Send className="ml-2 w-5 h-5" />
                  </Button>
                </form>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              {/* Contact Details */}
              <Card className="p-8 shadow-lg">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h3>
                
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                      <Mail className="w-6 h-6 text-[#C41C1F]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Email Us</h4>
                      <p className="text-gray-600"><EMAIL></p>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                      <Phone className="w-6 h-6 text-[#C41C1F]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Call Us</h4>
                      <p className="text-gray-600">+****************</p>
                      <p className="text-gray-600">+****************</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                      <MapPin className="w-6 h-6 text-[#C41C1F]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Visit Us</h4>
                      <p className="text-gray-600">123 Tech Street, Suite 100</p>
                      <p className="text-gray-600">San Francisco, CA 94105</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                      <Clock className="w-6 h-6 text-[#C41C1F]" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Business Hours</h4>
                      <p className="text-gray-600">Monday - Friday: 9:00 AM - 6:00 PM</p>
                      <p className="text-gray-600">Saturday: 10:00 AM - 4:00 PM</p>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Quick Actions */}
              <div className="grid grid-cols-2 gap-4">
                <Card className="p-6 text-center hover:shadow-lg transition-shadow duration-300 border-2 hover:border-[#C41C1F] group cursor-pointer">
                  <div className="w-12 h-12 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-[#C41C1F] transition-colors duration-300">
                    <MessageSquare className="w-6 h-6 text-[#C41C1F] group-hover:text-white transition-colors duration-300" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Live Chat</h4>
                  <p className="text-sm text-gray-600">Chat with our team</p>
                </Card>

                <Card className="p-6 text-center hover:shadow-lg transition-shadow duration-300 border-2 hover:border-[#C41C1F] group cursor-pointer">
                  <div className="w-12 h-12 bg-[#C41C1F] bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-[#C41C1F] transition-colors duration-300">
                    <Calendar className="w-6 h-6 text-[#C41C1F] group-hover:text-white transition-colors duration-300" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Book a Call</h4>
                  <p className="text-sm text-gray-600">Schedule a meeting</p>
                </Card>
              </div>

              {/* CTA */}
              <div className="bg-gradient-to-r from-[#C41C1F] to-[#9B1C1C] rounded-2xl p-8 text-white text-center">
                <h4 className="text-xl font-bold mb-3">Ready to Get Started?</h4>
                <p className="mb-6 opacity-90">
                  Let's discuss your project and see how we can help you achieve your goals.
                </p>
                <Button 
                  variant="outline" 
                  className="border-white text-white hover:bg-white hover:text-[#C41C1F] transition-all duration-300"
                >
                  Schedule Free Consultation
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
