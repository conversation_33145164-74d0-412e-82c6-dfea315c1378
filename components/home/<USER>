import { Card } from "@/components/ui/card";
import { Star, Quote } from "lucide-react";

export default function Testimonials() {
  const testimonials = [
    {
      name: "<PERSON>",
      position: "CEO, TechStart Inc.",
      company: "TechStart Inc.",
      content: "Applex transformed our business with their innovative software solution. The team's expertise and dedication exceeded our expectations. Our productivity increased by 300% after implementation.",
      rating: 5,
      avatar: "SJ"
    },
    {
      name: "<PERSON>",
      position: "CTO, DataFlow Systems",
      company: "DataFlow Systems",
      content: "Working with Applex was a game-changer for our company. They delivered a robust, scalable platform that handles millions of transactions daily. Exceptional quality and support.",
      rating: 5,
      avatar: "MC"
    },
    {
      name: "<PERSON>",
      position: "Founder, EcoCommerce",
      company: "EcoCommerce",
      content: "The e-commerce platform Applex built for us is simply outstanding. User-friendly, fast, and feature-rich. Our online sales increased by 250% within the first quarter.",
      rating: 5,
      avatar: "ER"
    },
    {
      name: "<PERSON>",
      position: "VP of Technology, FinanceFlow",
      company: "FinanceFlow",
      content: "Applex delivered our mobile banking app on time and within budget. The security features and user experience are top-notch. Our customers love the intuitive interface.",
      rating: 5,
      avatar: "DT"
    },
    {
      name: "<PERSON>",
      position: "Operations Director, LogiTech Solutions",
      company: "LogiTech Solutions",
      content: "The custom inventory management system from Applex streamlined our entire operation. Real-time tracking and automated workflows saved us countless hours and reduced errors by 95%.",
      rating: 5,
      avatar: "LW"
    },
    {
      name: "James Miller",
      position: "Marketing Director, CreativeAgency",
      company: "CreativeAgency",
      content: "Applex created a stunning portfolio website that perfectly represents our brand. The performance is incredible, and the CMS makes content management effortless. Highly recommended!",
      rating: 5,
      avatar: "JM"
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              What Our <span className="text-[#C41C1F]">Clients Say</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Don't just take our word for it. Here's what our satisfied clients have to say about working with Applex and the results we've delivered.
            </p>
          </div>

          {/* Testimonials Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-2 hover:border-[#C41C1F] relative">
                {/* Quote Icon */}
                <div className="absolute top-4 right-4">
                  <Quote className="w-8 h-8 text-[#C41C1F] opacity-20" />
                </div>

                {/* Rating */}
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* Content */}
                <p className="text-gray-600 mb-6 leading-relaxed italic">
                  "{testimonial.content}"
                </p>

                {/* Author */}
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-[#C41C1F] rounded-full flex items-center justify-center text-white font-bold mr-4">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-bold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.position}</div>
                    <div className="text-sm text-[#C41C1F] font-medium">{testimonial.company}</div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Trust Indicators */}
          <div className="bg-white rounded-2xl p-8 md:p-12 shadow-lg">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Trusted by <span className="text-[#C41C1F]">Industry Leaders</span>
              </h3>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                We've had the privilege of working with companies of all sizes, from startups to Fortune 500 enterprises.
              </p>
            </div>

            {/* Client Logos Placeholder */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
              {[
                "TechStart", "DataFlow", "EcoCommerce", "FinanceFlow", "LogiTech", "CreativeAgency"
              ].map((company, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <span className="text-xs font-bold text-gray-500">{company.slice(0, 2)}</span>
                  </div>
                  <div className="text-sm text-gray-500 font-medium">{company}</div>
                </div>
              ))}
            </div>

            {/* Stats */}
            <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-[#C41C1F] mb-2">98%</div>
                <div className="text-gray-600">Client Retention Rate</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-[#C41C1F] mb-2">4.9/5</div>
                <div className="text-gray-600">Average Rating</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-[#C41C1F] mb-2">500+</div>
                <div className="text-gray-600">Projects Completed</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-[#C41C1F] mb-2">50+</div>
                <div className="text-gray-600">Happy Clients</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
